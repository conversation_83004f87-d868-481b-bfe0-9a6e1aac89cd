package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.InviteNotification;
import net.minecraft.client.MinecraftClient;

/**
 * Manages opening and closing the town screen.
 */
public class TownScreenManager {

    /**
     * Opens the town screen using the normal Minecraft GUI.
     */
    public static void openTownScreen() {
        Pokecobbleclaim.LOGGER.info("Opening town screen");
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            // Check if there's a pending invitation
            if (InviteNotification.hasPendingInvite()) {
                // Open the invite response screen
                client.execute(() -> client.setScreen(new InviteResponseScreen(
                    client.currentScreen,
                    InviteNotification.getPendingInviteTownId(),
                    InviteNotification.getPendingInviteTownName()
                )));
            } else {
                // Open the normal town screen
                client.execute(() -> client.setScreen(new ModernTownScreen(client.currentScreen)));
            }
        }
    }

    /**
     * Opens the MyTownScreen directly, but only if the player is in a town.
     * This method provides validation before opening the screen.
     */
    public static void openMyTownScreen() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null && client.player != null) {
            // Check if player is in a town
            com.pokecobble.town.Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            if (playerTown != null) {
                // Player is in a town, open MyTownScreen
                client.execute(() -> client.setScreen(new com.pokecobble.town.gui.MyTownScreen(client.currentScreen)));
            } else {
                // Player is not in a town, show notification
                com.pokecobble.town.client.NotificationRenderer.addNotification("You are not a member of any town");
            }
        }
    }
}
