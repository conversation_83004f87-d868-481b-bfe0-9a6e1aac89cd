package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * A modern, professional interface for town management with enhanced UX and visual design.
 * Features a sophisticated dark theme, improved typography, and smooth animations.
 */
public class ModernTownScreen extends Screen implements TownDataSynchronizer.TownListUpdateCallback, com.pokecobble.ui.UIDataRefreshManager.RefreshableComponent {
    private final Screen parent;

    // Compact Design Constants
    private static final int CORNER_RADIUS = 4;
    private static final int SHADOW_SIZE = 2;
    private static final int ANIMATION_DURATION = 150; // milliseconds

    // Modern Color Palette
    private static final int PRIMARY_BG = 0xE8121212;        // Dark background
    private static final int SECONDARY_BG = 0xF01A1A1A;     // Slightly lighter
    private static final int CARD_BG = 0xF0252525;          // Card background
    private static final int ACCENT_PRIMARY = 0xFF4A90E2;   // Modern blue
    private static final int ACCENT_SUCCESS = 0xFF27AE60;   // Success green
    private static final int ACCENT_WARNING = 0xFFF39C12;   // Warning orange
    private static final int ACCENT_DANGER = 0xFFE74C3C;    // Danger red
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text
    private static final int BORDER_COLOR = 0xFF333333;     // Border color
    private static final int HOVER_OVERLAY = 0x20FFFFFF;    // Hover effect

    // Panel dimensions - compact and efficient
    private int panelWidth = 480;
    private int panelHeight = 320;

    // Compact spacing system (4px grid)
    private static final int SPACING_XS = 2;
    private static final int SPACING_SM = 4;
    private static final int SPACING_MD = 8;
    private static final int SPACING_LG = 12;
    private static final int SPACING_XL = 16;

    // Category tab dimensions - more compact
    private static final int TAB_HEIGHT = 28;
    private static final int TAB_MIN_WIDTH = 80;
    private static final int TAB_SPACING = 1;

    // Town card dimensions - more compact
    private static final int CARD_HEIGHT = 32;
    private static final int CARD_SPACING = SPACING_XS;
    private static final int CARD_PADDING = SPACING_SM;

    // Legacy constants for compatibility - compact sizes
    private static final int ENTRY_HEIGHT = 32; // More compact card height for smaller town list entries
    private static final int ENTRY_SPACING = 1; // Minimal spacing between entries

    // Scrolling variables
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 20;
    private float scrollVelocity = 0.0f;

    // Categories
    private final List<TownCategory> categories = new ArrayList<>();
    private TownCategory selectedCategory = null;

    // Animation variables
    private float animationProgress = 0.0f;
    private long lastRenderTime = 0;
    private float hoverAnimations[] = new float[10]; // For hover animations

    // UI elements and button positions
    private int backButtonX;
    private int backButtonY;
    private int createButtonX;
    private int createButtonY;

    // Search functionality
    private String searchQuery = "";
    private boolean showSearchField = false;

    // Interface state tracking
    private boolean inMyTownInterface = false;

    // Status message with modern styling
    private Text statusText = Text.empty();
    private int statusColor = TEXT_PRIMARY;
    private long statusShowTime = 0;
    private static final long STATUS_DISPLAY_DURATION = 3000; // 3 seconds

    public ModernTownScreen(Screen parent) {
        super(Text.literal("Towns"));
        this.parent = parent;
        setupCategories();
    }

    private void setupCategories() {
        // Save positions of existing categories
        Map<String, Integer[]> categoryPositions = new HashMap<>();
        for (TownCategory category : categories) {
            categoryPositions.put(category.getId(), new Integer[]{category.getX(), category.getY()});
        }

        // Save the currently selected category ID
        String selectedCategoryId = selectedCategory != null ? selectedCategory.getId() : null;

        // Clear existing categories
        categories.clear();

        // Create "All Towns" category
        TownCategory allTownsCategory = new TownCategory(
            "all_towns",
            "All Towns",
            "View and join all available towns",
            0x8055FFAA
        );

        // Add all towns to this category
        // Use ClientTownManager for better client-side caching and synchronization
        List<Town> allTowns = new ArrayList<>(com.pokecobble.town.client.ClientTownManager.getInstance().getAllTowns());

        // Fallback to TownManager if ClientTownManager has no towns (e.g., during initial load or cache expired)
        if (allTowns.isEmpty()) {
            allTowns = new ArrayList<>(TownManager.getInstance().getAllTowns());

            // If both sources are empty, request fresh data from server
            if (allTowns.isEmpty()) {
                com.pokecobble.town.network.town.TownNetworkHandler.requestTownList();
                Pokecobbleclaim.LOGGER.info("No towns found in cache, requesting fresh town list from server");
            }
        }
        for (Town town : allTowns) {
            TownEntry entry = new TownEntry(
                town.getId().toString(),
                town.getName(),
                town.getPlayerCount() + " " + (town.getPlayerCount() == 1 ? "player" : "players"),
                town
            );
            allTownsCategory.addEntry(entry);
        }

        // Create "My Town" category
        TownCategory myTownCategory = new TownCategory(
            "my_town",
            "My Town",
            "Manage your current town",
            0x80FFAA55
        );

        // Add the player's town if they're in one
        if (MinecraftClient.getInstance().player != null) {
            // Use ClientTownManager for better client-side caching
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

            // Fallback to TownManager if ClientTownManager doesn't have the player's town
            if (playerTown == null) {
                playerTown = TownManager.getInstance().getPlayerTown(MinecraftClient.getInstance().player.getUuid());
            }
            if (playerTown != null) {
                TownEntry entry = new TownEntry(
                    playerTown.getId().toString(),
                    playerTown.getName(),
                    playerTown.getPlayerCount() + " " + (playerTown.getPlayerCount() == 1 ? "player" : "players"),
                    playerTown
                );
                myTownCategory.addEntry(entry);
            }
        }

        // Create "Town Ranking" category
        TownCategory townRankingCategory = new TownCategory(
            "town_ranking",
            "Town Ranking",
            "Towns ranked by player count",
            0x8055FFFF
        );

        // Add towns sorted by player count (most to least)
        List<Town> sortedTowns = new ArrayList<>(allTowns);
        sortedTowns.sort((t1, t2) -> Integer.compare(t2.getPlayerCount(), t1.getPlayerCount()));

        for (int i = 0; i < Math.min(5, sortedTowns.size()); i++) {
            Town town = sortedTowns.get(i);
            TownEntry entry = new TownEntry(
                town.getId().toString(),
                town.getName(),
                town.getPlayerCount() + " " + (town.getPlayerCount() == 1 ? "player" : "players"),
                town
            );
            townRankingCategory.addEntry(entry);
        }

        // Add categories to the list
        categories.add(allTownsCategory);
        categories.add(myTownCategory);
        categories.add(townRankingCategory);

        // Restore positions of categories
        for (TownCategory category : categories) {
            Integer[] position = categoryPositions.get(category.getId());
            if (position != null) {
                category.setPosition(position[0], position[1]);
            }
        }

        // Restore the selected category if possible
        if (selectedCategoryId != null) {
            for (TownCategory category : categories) {
                if (category.getId().equals(selectedCategoryId)) {
                    selectedCategory = category;
                    break;
                }
            }
        }

        // Set default selected category if none is selected
        if (selectedCategory == null && !categories.isEmpty()) {
            selectedCategory = categories.get(0);
        }
    }





    @Override
    protected void init() {
        super.init();

        // Register for town list updates
        TownDataSynchronizer.registerTownListUpdateCallback(this);

        // Request the latest town list from the server to ensure we have current data
        com.pokecobble.town.network.town.TownNetworkHandler.requestTownList();

        // Reset animation and timing
        animationProgress = 0.0f;
        lastRenderTime = System.currentTimeMillis();

        // Initialize hover animations
        for (int i = 0; i < hoverAnimations.length; i++) {
            hoverAnimations[i] = 0.0f;
        }

        // Calculate fully responsive panel dimensions
        int minWidth = 320; // Absolute minimum for functionality
        int minHeight = 240; // Absolute minimum for functionality
        int preferredWidth = 480;
        int preferredHeight = 320;

        // Ensure panel fits within screen with margins
        int availableWidth = width - SPACING_MD * 4; // Extra margin for safety
        int availableHeight = height - SPACING_MD * 4; // Extra margin for safety

        // Use preferred size if it fits, otherwise scale down
        panelWidth = Math.max(minWidth, Math.min(preferredWidth, availableWidth));
        panelHeight = Math.max(minHeight, Math.min(preferredHeight, availableHeight));

        // Center the panel
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Position category tabs with responsive sizing
        int tabsStartX = leftX + SPACING_MD;
        int tabsY = topY + SPACING_LG + 12; // Below the compact header
        int availableTabWidth = panelWidth - SPACING_MD * 2;

        // Calculate total width needed for all tabs
        int totalTabWidth = 0;
        int[] tabWidths = new int[categories.size()];
        for (int i = 0; i < categories.size(); i++) {
            TownCategory category = categories.get(i);
            int textWidth = this.textRenderer.getWidth(category.getName());
            int tabWidth = Math.max(TAB_MIN_WIDTH, textWidth + SPACING_MD);
            tabWidths[i] = tabWidth;
            totalTabWidth += tabWidth + (i > 0 ? TAB_SPACING : 0);
        }

        // If tabs don't fit, scale them down proportionally
        if (totalTabWidth > availableTabWidth && categories.size() > 0) {
            float scaleFactor = (float) availableTabWidth / totalTabWidth;
            for (int i = 0; i < tabWidths.length; i++) {
                tabWidths[i] = Math.max(60, (int)(tabWidths[i] * scaleFactor)); // Minimum 60px width
            }
        }

        // Position tabs
        int currentX = tabsStartX;
        for (int i = 0; i < categories.size(); i++) {
            TownCategory category = categories.get(i);
            category.setPosition(currentX, tabsY);
            category.setWidth(tabWidths[i]);
            currentX += tabWidths[i] + TAB_SPACING;
        }

        // Position action buttons responsively
        int buttonHeight = 24;
        backButtonX = leftX + SPACING_MD;
        backButtonY = topY + panelHeight - SPACING_LG - buttonHeight;

        // Ensure create button fits within panel
        int createButtonWidth = Math.min(100, panelWidth - SPACING_MD * 2 - 60 - SPACING_MD); // 60 = back button width
        createButtonX = leftX + panelWidth - SPACING_MD - createButtonWidth;
        createButtonY = backButtonY;
    }

    private void selectCategory(TownCategory category) {
        // Check if this is the My Town or Town Ranking category
        if (category.getName().equals("My Town") || category.getName().equals("Town Ranking")) {
            // Check if player is actually in a town before opening MyTownScreen
            if (client.player != null) {
                Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
                if (playerTown == null) {
                    // Player is not in a town, show error message
                    setStatus("You are not a member of any town", Formatting.RED);
                    playClickSound();
                    return;
                }
            }

            // Open the My Town screen
            this.client.setScreen(new MyTownScreen(this));
            return;
        }

        // Set the new category
        this.selectedCategory = category;

        // Reset scroll offset
        this.scrollOffset = 0;

        // Play click sound
        playClickSound();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update animation and timing
        long currentTime = System.currentTimeMillis();
        float deltaTime = (currentTime - lastRenderTime) / 1000.0f;
        lastRenderTime = currentTime;

        // Update smooth scrolling
        if (Math.abs(scrollVelocity) > 0.1f) {
            scrollOffset += scrollVelocity * deltaTime * 60; // 60 FPS target
            scrollVelocity *= 0.85f; // Damping
        }

        // Render modern background
        this.renderBackground(context);

        // Calculate centered panel position
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Draw modern panel with shadow effect
        drawModernPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw modern header
        drawModernHeader(context, leftX, topY, panelWidth);

        // Draw modern category tabs
        drawModernTabs(context, mouseX, mouseY);

        // Calculate compact content area
        int contentX = leftX + SPACING_MD;
        int contentY = topY + SPACING_LG + 12 + TAB_HEIGHT + SPACING_SM;
        int contentWidth = panelWidth - SPACING_MD * 2;
        int contentHeight = panelHeight - (SPACING_LG + 12 + TAB_HEIGHT + SPACING_SM + SPACING_LG + 24);

        // Draw modern content area
        drawModernCard(context, contentX, contentY, contentWidth, contentHeight, CARD_BG, false);

        // Draw modern action buttons
        drawModernActionButtons(context, leftX, topY, mouseX, mouseY);

        // Draw selected category content
        if (selectedCategory != null) {
            // Draw category header with icon - more compact
            String categoryHeader = selectedCategory.getName();
            context.drawTextWithShadow(this.textRenderer, Text.literal(categoryHeader).formatted(Formatting.BOLD),
                contentX + 15, contentY + 8, selectedCategory.getColor() & 0xFFFFFF);

            // Draw category description - smaller and to the right
            context.drawTextWithShadow(this.textRenderer, Text.literal(selectedCategory.getDescription()),
                contentX + 15 + textRenderer.getWidth(categoryHeader) + 10, contentY + 8, 0xAAAAAA);

            // Draw refresh button for All Towns and Popular Towns categories
            if (selectedCategory.getId().equals("all_towns") || selectedCategory.getId().equals("popular_towns")) {
                // Position the refresh button on the far right with some margin
                int refreshButtonX = contentX + contentWidth - 30;
                int refreshButtonY = contentY + 8;

                // Check if mouse is hovering over the refresh button
                boolean isRefreshHovered = mouseX >= refreshButtonX - 2 && mouseX <= refreshButtonX + 12 &&
                                         mouseY >= refreshButtonY - 2 && mouseY <= refreshButtonY + 12;

                // Draw the refresh button (just an emoji)
                int refreshColor = isRefreshHovered ? 0xFFFFFFFF : 0xBBFFFFFF; // Brighter when hovered
                context.drawTextWithShadow(this.textRenderer, "🔄", refreshButtonX, refreshButtonY, refreshColor);
            }

            // Draw divider - closer to header
            context.fill(contentX + 10, contentY + 20, contentX + contentWidth - 10, contentY + 21, 0x40FFFFFF);

            // Calculate visible area for town entries
            int entriesAreaY = contentY + 25; // Start entries closer to header
            int entriesAreaHeight = contentHeight - 35; // More vertical space for entries



            // Calculate total height of all entries
            List<TownEntry> entries = selectedCategory.getEntries();
            int totalHeight = 0;
            for (TownEntry entry : entries) {
                // Use dynamic height calculation for expanded entries
                int entryHeight = calculateExpandedEntryHeight(entry, contentWidth - 30);
                totalHeight += entryHeight + ENTRY_SPACING;
            }
            // Add extra padding at the bottom to ensure we can scroll to see the last entry fully
            totalHeight += 30;

            // Calculate max scroll
            int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);
            scrollOffset = Math.min(scrollOffset, maxScroll);

            // Draw scrollbar if needed - more modern style
            if (maxScroll > 0) {
                // Draw scrollbar track - thinner and more subtle
                context.fill(contentX + contentWidth - 8, entriesAreaY, contentX + contentWidth - 4, entriesAreaY + entriesAreaHeight, 0x20FFFFFF);

                // Calculate scrollbar height and position - ensure it's not too small
                int scrollbarHeight = Math.max(40, entriesAreaHeight * entriesAreaHeight / (totalHeight + entriesAreaHeight));

                // Calculate scrollbar position - ensure it can reach the bottom
                float scrollRatio = (float)scrollOffset / maxScroll;
                int scrollbarY = entriesAreaY + (int)((entriesAreaHeight - scrollbarHeight) * scrollRatio);

                // Ensure scrollbar doesn't go out of bounds
                scrollbarY = Math.max(entriesAreaY, Math.min(scrollbarY, entriesAreaY + entriesAreaHeight - scrollbarHeight));

                // Draw scrollbar handle - rounded corners effect with more opacity
                drawRoundedRect(context, contentX + contentWidth - 8, scrollbarY, 4, scrollbarHeight, 0xC0FFFFFF);
            }

            // Apply scissor to clip content to visible area
            context.enableScissor(
                contentX + 10,
                entriesAreaY,
                contentX + contentWidth - 10,
                entriesAreaY + entriesAreaHeight
            );

            // Draw town entries with scrolling
            int entryY = entriesAreaY - scrollOffset;
            int buttonWidth = contentWidth - 30;

            for (TownEntry entry : entries) {
                // Calculate entry height based on expanded state using dynamic calculation
                int entryHeight = calculateExpandedEntryHeight(entry, buttonWidth);

                // Skip if entry is completely outside visible area
                if (entryY + entryHeight < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                    entryY += entryHeight + ENTRY_SPACING;
                    continue;
                }

                // Check if mouse is hovering over this entry
                boolean isHovered = mouseX >= contentX + 10 && mouseX <= contentX + 10 + buttonWidth &&
                                   mouseY >= entryY && mouseY <= entryY + ENTRY_HEIGHT &&
                                   mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight;

                // Draw entry background with semi-transparent glass effect
                int entryColor = isHovered ? 0x60404040 : 0x40303030;
                if (entry.isExpanded()) entryColor = 0x80505050; // Darker when expanded

                // Draw main background
                context.fill(contentX + 10, entryY, contentX + 10 + buttonWidth, entryY + entryHeight, entryColor);

                // Draw subtle glass highlight at the top
                context.fill(contentX + 10, entryY, contentX + 10 + buttonWidth, entryY + 1, 0x20FFFFFF);
                context.fill(contentX + 10, entryY, contentX + 11, entryY + entryHeight, 0x20FFFFFF);

                // Draw subtle shadow at the bottom
                context.fill(contentX + 10, entryY + entryHeight - 1, contentX + 10 + buttonWidth, entryY + entryHeight, 0x20000000);

                // Draw colored accent on the left side - thinner for compact look
                int accentColor = selectedCategory.getColor();
                if (entry.isExpanded()) {
                    // Full height accent for expanded entries
                    context.fill(contentX + 10, entryY, contentX + 12, entryY + entryHeight, accentColor);
                } else {
                    // Partial height accent for collapsed entries
                    context.fill(contentX + 10, entryY + 2, contentX + 12, entryY + ENTRY_HEIGHT - 2, accentColor);
                }

                // Draw town name - compact but still bold
                context.drawTextWithShadow(this.textRenderer, Text.literal(entry.getName()).formatted(Formatting.BOLD),
                    contentX + 20, entryY + (ENTRY_HEIGHT - 8) / 2, 0xFFFFFF);

                // Draw player count with smaller icon
                String playerText = entry.getDescription();
                int playerCountWidth = this.textRenderer.getWidth(playerText);
                context.drawTextWithShadow(this.textRenderer, Text.literal("👥" + playerText),
                    contentX + buttonWidth - playerCountWidth - 60, entryY + (ENTRY_HEIGHT - 8) / 2, 0xAAAAAA);

                // Draw expand/collapse indicator - smaller
                String expandIndicator = entry.isExpanded() ? "▼" : "▶";
                context.drawTextWithShadow(this.textRenderer, expandIndicator,
                    contentX + 14, entryY + (ENTRY_HEIGHT - 8) / 2, 0xFFFFFF);

                // Draw join button - smaller and more compact
                boolean isPlayerInTown = false;
                if (client.player != null) {
                    Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
                    if (playerTown != null && playerTown.getId().equals(entry.getTown().getId())) {
                        isPlayerInTown = true;
                    }
                }

                // Check if town is closed
                boolean isTownClosed = !entry.getTown().isOpen();

                // Determine button text and properties based on town status and player membership
                String buttonText;
                int joinButtonWidth;
                int joinButtonColor;

                if (isPlayerInTown) {
                    buttonText = "✓";
                    joinButtonWidth = 20;
                    joinButtonColor = 0xC08BC34A; // Semi-transparent light green
                } else if (isTownClosed) {
                    buttonText = "Close";
                    joinButtonWidth = 40; // Same size as join button
                    joinButtonColor = 0xC0F44336; // Semi-transparent red
                } else {
                    buttonText = "Join";
                    joinButtonWidth = 40;
                    joinButtonColor = 0xC04CAF50; // Semi-transparent green
                }

                int buttonX = contentX + buttonWidth - 45; // Positioned further right
                int buttonY = entryY + (ENTRY_HEIGHT - 16) / 2; // Centered vertically

                // Check if button is hovered
                boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + joinButtonWidth &&
                                      mouseY >= buttonY && mouseY <= buttonY + 16;

                // Draw semi-transparent button background
                int bgColor = buttonHovered ? joinButtonColor : (joinButtonColor & 0x00FFFFFF) | 0x80000000;

                // Draw main background
                context.fill(buttonX, buttonY, buttonX + joinButtonWidth, buttonY + 16, bgColor);

                // Draw subtle glass highlight at the top
                context.fill(buttonX, buttonY, buttonX + joinButtonWidth, buttonY + 1, 0x20FFFFFF);
                context.fill(buttonX, buttonY, buttonX + 1, buttonY + 16, 0x20FFFFFF);

                // Draw subtle shadow at the bottom
                context.fill(buttonX, buttonY + 15, buttonX + joinButtonWidth, buttonY + 16, 0x20000000);
                context.fill(buttonX + joinButtonWidth - 1, buttonY, buttonX + joinButtonWidth, buttonY + 16, 0x20000000);

                // Draw button text
                int buttonTextColor;
                if (isPlayerInTown) {
                    buttonTextColor = 0xFF00FF00; // Green for checkmark
                } else if (isTownClosed) {
                    buttonTextColor = 0xFFFFFF; // White for closed button
                } else {
                    buttonTextColor = 0xFFFFFF; // White for join button
                }
                context.drawCenteredTextWithShadow(this.textRenderer, buttonText,
                    buttonX + joinButtonWidth / 2, buttonY + 4, buttonTextColor);

                // Draw expanded information if this entry is expanded - reorganized layout
                if (entry.isExpanded()) {
                    int infoY = entryY + ENTRY_HEIGHT + 2; // Less spacing
                    int infoX = contentX + 20; // Less indentation

                    // Draw town description at the top with proper text wrapping for 100 characters
                    String fullDescription = entry.getTown().getDescription();
                    context.drawTextWithShadow(this.textRenderer, Text.literal("Description:").formatted(Formatting.ITALIC),
                        infoX, infoY, 0xFFFFFF);

                    // Wrap description text to support 100 characters and multiple lines
                    int descriptionMaxWidth = buttonWidth - 40; // Available width for description
                    java.util.List<String> wrappedDescription = wrapTextToCharacterLimit(fullDescription, 100, descriptionMaxWidth);

                    int descY = infoY + 12;
                    for (String line : wrappedDescription) {
                        context.drawTextWithShadow(this.textRenderer, Text.literal(line),
                            infoX + 5, descY, 0xCCCCCC);
                        descY += 10; // Line spacing
                    }

                    // Calculate position for expandable info at the bottom of the info zone
                    int expandableInfoY = descY + 10; // Add some spacing after description

                    // Draw info in a more compact grid layout with 3 columns at the bottom
                    int colWidth = (buttonWidth - 20) / 3; // Three equal columns
                    int col2X = infoX + colWidth; // X position of second column
                    int col3X = infoX + colWidth * 2; // X position of third column

                    // Draw town status (open/closed) - first column
                    String openStatus = entry.getTown().isOpen() ? "Open" : "Closed";
                    int statusColor = entry.getTown().isOpen() ? 0x55FF55 : 0xFF5555;
                    context.drawTextWithShadow(this.textRenderer, Text.literal("Status:").formatted(Formatting.ITALIC),
                        infoX, expandableInfoY, 0xFFFFFF);
                    context.drawTextWithShadow(this.textRenderer, Text.literal(openStatus),
                        infoX + 5, expandableInfoY + 10, statusColor);

                    // Draw player limit - second column
                    String playerLimit = entry.getTown().getPlayerCount() + "/" + entry.getTown().getMaxPlayers();
                    context.drawTextWithShadow(this.textRenderer, Text.literal("Players:").formatted(Formatting.ITALIC),
                        col2X, expandableInfoY, 0xFFFFFF);
                    context.drawTextWithShadow(this.textRenderer, Text.literal(playerLimit),
                        col2X + 5, expandableInfoY + 10, 0xCCCCCC);

                    // Draw creation date or other info - third column
                    context.drawTextWithShadow(this.textRenderer, Text.literal("Created:").formatted(Formatting.ITALIC),
                        col3X, expandableInfoY, 0xFFFFFF);
                    context.drawTextWithShadow(this.textRenderer, Text.literal("Recently"),
                        col3X + 5, expandableInfoY + 10, 0xCCCCCC);
                }

                // Draw tooltip on hover
                if (isHovered) {
                    context.drawTooltip(this.textRenderer, Text.literal("Click to view details"), mouseX, mouseY);
                }

                // Move to next entry
                entryY += entryHeight + ENTRY_SPACING;
            }

            // Disable scissor
            context.disableScissor();
        }

        // Draw modern status message with auto-hide
        drawModernStatusMessage(context, leftX, topY);

        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Draws a compact modern panel with subtle shadow and gradient background
     */
    private void drawModernPanel(DrawContext context, int x, int y, int width, int height) {
        // Draw compact shadow
        for (int i = 0; i < SHADOW_SIZE; i++) {
            int shadowAlpha = (SHADOW_SIZE - i) * 15;
            int shadowColor = shadowAlpha << 24;
            context.fill(x + i, y + i, x + width + i, y + height + i, shadowColor);
        }

        // Draw main panel background with gradient
        context.fillGradient(x, y, x + width, y + height, PRIMARY_BG, SECONDARY_BG);

        // Draw subtle border
        context.drawBorder(x, y, width, height, BORDER_COLOR);

        // Add subtle inner glow
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x15FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);
    }

    /**
     * Draws the compact modern header section
     */
    private void drawModernHeader(DrawContext context, int x, int y, int width) {
        // Compact header background
        context.fillGradient(x, y, x + width, y + SPACING_LG + 12, SECONDARY_BG, PRIMARY_BG);

        // Header title - smaller
        String title = "Town Management";
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = x + (width - titleWidth) / 2;
        int titleY = y + SPACING_SM;

        // Subtle title glow effect
        context.fillGradient(
            titleX - SPACING_SM, titleY - 1,
            titleX + titleWidth + SPACING_SM, titleY + 10,
            0x20FFFFFF, 0x08FFFFFF
        );

        // Draw title
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, TEXT_PRIMARY);

        // Compact header divider
        context.fill(x + SPACING_MD, y + SPACING_LG + 10, x + width - SPACING_MD, y + SPACING_LG + 11, BORDER_COLOR);
    }

    /**
     * Draws modern category tabs
     */
    private void drawModernTabs(DrawContext context, int mouseX, int mouseY) {
        for (int i = 0; i < categories.size(); i++) {
            TownCategory category = categories.get(i);
            boolean isSelected = category == selectedCategory;
            int tabX = category.getX();
            int tabY = category.getY();
            int tabWidth = category.getWidth();

            // Check hover state
            boolean isHovered = mouseX >= tabX && mouseX <= tabX + tabWidth &&
                               mouseY >= tabY && mouseY <= tabY + TAB_HEIGHT;

            // Update hover animation
            if (i < hoverAnimations.length) {
                float targetAnimation = isHovered ? 1.0f : 0.0f;
                hoverAnimations[i] += (targetAnimation - hoverAnimations[i]) * 0.2f;
            }

            // Draw tab background
            int bgColor = isSelected ? ACCENT_PRIMARY : CARD_BG;
            if (isHovered && !isSelected) {
                bgColor = blendColors(CARD_BG, HOVER_OVERLAY, 0.3f);
            }

            drawModernCard(context, tabX, tabY, tabWidth, TAB_HEIGHT, bgColor, isSelected);

            // Draw tab content
            String tabText = getCategoryIcon(i) + " " + category.getName();
            int textColor = isSelected ? TEXT_PRIMARY : TEXT_SECONDARY;
            int textX = tabX + (tabWidth - this.textRenderer.getWidth(tabText)) / 2;
            int textY = tabY + (TAB_HEIGHT - 8) / 2;

            context.drawTextWithShadow(this.textRenderer, tabText, textX, textY, textColor);

            // Draw selection indicator
            if (isSelected) {
                context.fill(tabX, tabY + TAB_HEIGHT - 2, tabX + tabWidth, tabY + TAB_HEIGHT, ACCENT_PRIMARY);
            }
        }
    }

    /**
     * Gets the icon for a category based on its index
     */
    private String getCategoryIcon(int index) {
        switch (index) {
            case 0: return "🏙"; // All Towns
            case 1: return "🏠"; // My Town
            case 2: return "🏆"; // Town Ranking
            default: return "🌐"; // Other
        }
    }

    /**
     * Draws a compact modern card with subtle shadow and hover effects
     */
    private void drawModernCard(DrawContext context, int x, int y, int width, int height, int bgColor, boolean elevated) {
        // Draw compact card shadow if elevated
        if (elevated) {
            int shadowAlpha = 10;
            int shadowColor = shadowAlpha << 24;
            context.fill(x + 1, y + 1, x + width + 1, y + height + 1, shadowColor);
        }

        // Draw card background
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw subtle border
        context.drawBorder(x, y, width, height, BORDER_COLOR);

        // Add subtle inner highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x10FFFFFF);
    }



    /**
     * Draws responsive compact modern action buttons (Back and Create Town)
     */
    private void drawModernActionButtons(DrawContext context, int panelX, int panelY, int mouseX, int mouseY) {
        int buttonHeight = 24;
        int buttonY = panelY + panelHeight - SPACING_LG - buttonHeight;

        // Calculate responsive button widths
        int backButtonWidth = 60;
        int createButtonWidth = Math.min(100, panelWidth - SPACING_MD * 2 - backButtonWidth - SPACING_MD);

        // Compact Back button
        boolean backHovered = mouseX >= backButtonX && mouseX <= backButtonX + backButtonWidth &&
                             mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        int backColor = backHovered ? ACCENT_DANGER : blendColors(ACCENT_DANGER, CARD_BG, 0.7f);
        drawModernCard(context, backButtonX, buttonY, backButtonWidth, buttonHeight, backColor, backHovered);

        int backTextColor = backHovered ? TEXT_PRIMARY : TEXT_SECONDARY;
        context.drawCenteredTextWithShadow(this.textRenderer, "← Back",
            backButtonX + backButtonWidth / 2, buttonY + (buttonHeight - 8) / 2, backTextColor);

        // Responsive Create Town button
        boolean createHovered = mouseX >= createButtonX && mouseX <= createButtonX + createButtonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        int createColor = createHovered ? ACCENT_PRIMARY : blendColors(ACCENT_PRIMARY, CARD_BG, 0.7f);
        drawModernCard(context, createButtonX, buttonY, createButtonWidth, buttonHeight, createColor, createHovered);

        int createTextColor = createHovered ? TEXT_PRIMARY : TEXT_SECONDARY;
        // Adjust text based on button width
        String createText = createButtonWidth >= 80 ? "+ Create" : "+";
        context.drawCenteredTextWithShadow(this.textRenderer, createText,
            createButtonX + createButtonWidth / 2, buttonY + (buttonHeight - 8) / 2, createTextColor);
    }

    /**
     * Draws modern status message with auto-hide functionality
     */
    private void drawModernStatusMessage(DrawContext context, int panelX, int panelY) {
        if (statusText != Text.empty()) {
            long currentTime = System.currentTimeMillis();
            long timeSinceShow = currentTime - statusShowTime;

            // Auto-hide after duration
            if (timeSinceShow > STATUS_DISPLAY_DURATION) {
                statusText = Text.empty();
                return;
            }

            // Calculate fade-out animation
            float alpha = 1.0f;
            if (timeSinceShow > STATUS_DISPLAY_DURATION - 500) { // Fade out in last 500ms
                alpha = (STATUS_DISPLAY_DURATION - timeSinceShow) / 500.0f;
                alpha = Math.max(0.0f, Math.min(1.0f, alpha));
            }

            // Calculate responsive position (bottom center of panel, but ensure it fits on screen)
            int statusY = Math.min(panelY + panelHeight + SPACING_MD, height - 25);
            int statusX = width / 2;

            // Apply alpha to status color
            int alphaValue = (int)(alpha * 255);
            int finalStatusColor = (statusColor & 0x00FFFFFF) | (alphaValue << 24);

            // Draw status background with responsive sizing
            int textWidth = this.textRenderer.getWidth(statusText);
            int maxBgWidth = Math.min(textWidth + SPACING_MD * 2, width - SPACING_MD * 2);
            int bgWidth = maxBgWidth;
            int bgHeight = 20;
            int bgX = Math.max(SPACING_MD, Math.min(statusX - bgWidth / 2, width - bgWidth - SPACING_MD));
            int bgY = statusY - 2;

            int bgColor = (CARD_BG & 0x00FFFFFF) | (alphaValue << 24);
            drawModernCard(context, bgX, bgY, bgWidth, bgHeight, bgColor, false);

            // Draw status text
            context.drawCenteredTextWithShadow(this.textRenderer, statusText, statusX, statusY, finalStatusColor);
        }
    }

    /**
     * Blends two colors together
     */
    private int blendColors(int color1, int color2, float ratio) {
        int a1 = (color1 >> 24) & 0xFF;
        int r1 = (color1 >> 16) & 0xFF;
        int g1 = (color1 >> 8) & 0xFF;
        int b1 = color1 & 0xFF;

        int a2 = (color2 >> 24) & 0xFF;
        int r2 = (color2 >> 16) & 0xFF;
        int g2 = (color2 >> 8) & 0xFF;
        int b2 = color2 & 0xFF;

        int a = (int) (a1 + (a2 - a1) * ratio);
        int r = (int) (r1 + (r2 - r1) * ratio);
        int g = (int) (g1 + (g2 - g1) * ratio);
        int b = (int) (b1 + (b2 - b1) * ratio);

        return (a << 24) | (r << 16) | (g << 8) | b;
    }

    /**
     * Draws a rounded rectangle effect (actual corners aren't rounded, but it creates that visual effect)
     */
    private void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int color) {
        // Main rectangle
        context.fill(x, y, x + width, y + height, color);

        // Lighter top and left edges for rounded effect
        int lightEdge = (color & 0x00FFFFFF) | 0x10FFFFFF;
        context.fill(x, y, x + width, y + 1, lightEdge);
        context.fill(x, y, x + 1, y + height, lightEdge);

        // Darker bottom and right edges for rounded effect
        int darkEdge = (color & 0x00FFFFFF) | 0x10000000;
        context.fill(x, y + height - 1, x + width, y + height, darkEdge);
        context.fill(x + width - 1, y, x + width, y + height, darkEdge);
    }

    /**
     * Draws a modern button background with gradient and subtle 3D effect
     *
     * @param context The draw context
     * @param x X position
     * @param y Y position
     * @param width Button width
     * @param height Button height
     * @param baseColor Base color for the button
     * @param isHovered Whether the button is being hovered
     * @param isActive Whether the button is active/enabled
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int baseColor, boolean isHovered, boolean isActive) {
        // Extract RGB components
        int r = (baseColor >> 16) & 0xFF;
        int g = (baseColor >> 8) & 0xFF;
        int b = baseColor & 0xFF;

        // Adjust colors based on state
        if (!isActive) {
            // Desaturate and darken for inactive buttons
            int avg = (r + g + b) / 3;
            r = (r + avg) / 2;
            g = (g + avg) / 2;
            b = (b + avg) / 2;
            r = r * 3/4;
            g = g * 3/4;
            b = b * 3/4;
        } else if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Create colors for gradient
        int topColor = ((r) << 16) | ((g) << 8) | (b) | 0xFF000000;
        int bottomColor = ((r * 3/4) << 16) | ((g * 3/4) << 8) | (b * 3/4) | 0xFF000000;

        // Draw gradient background
        context.fillGradient(x, y, x + width, y + height, topColor, bottomColor);

        // Draw subtle 3D effect
        int highlightColor = 0x30FFFFFF; // Subtle white highlight
        int shadowColor = 0x30000000; // Subtle shadow

        // Top highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, highlightColor);
        // Left highlight
        context.fill(x + 1, y + 1, x + 2, y + height - 1, highlightColor);

        // Bottom shadow
        context.fill(x + 2, y + height - 2, x + width - 1, y + height - 1, shadowColor);
        // Right shadow
        context.fill(x + width - 2, y + 2, x + width - 1, y + height - 2, shadowColor);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Check for clicks on custom buttons
        if (button == 0) { // Left click
            // Check for category tab clicks
            for (int i = 0; i < categories.size(); i++) {
                TownCategory category = categories.get(i);
                int tabX = category.getX();
                int tabY = category.getY();
                int tabWidth = category.getWidth();

                if (mouseX >= tabX && mouseX <= tabX + tabWidth &&
                    mouseY >= tabY && mouseY <= tabY + TAB_HEIGHT) {
                    // Play click sound
                    playClickSound();
                    // Select this category
                    selectCategory(category);
                    return true;
                }
            }



            // Check for responsive action button clicks
            int buttonHeight = 24;
            int buttonY = (height - panelHeight) / 2 + panelHeight - SPACING_LG - buttonHeight;

            // Calculate responsive button widths
            int backButtonWidth = 60;
            int createButtonWidth = Math.min(100, panelWidth - SPACING_MD * 2 - backButtonWidth - SPACING_MD);

            // Check for responsive back button click
            if (mouseX >= backButtonX && mouseX <= backButtonX + backButtonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                // Play click sound
                playClickSound();
                // Close screen
                this.close();
                return true;
            }

            // Check for responsive create town button click
            if (mouseX >= createButtonX && mouseX <= createButtonX + createButtonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                // Play click sound
                playClickSound();
                // Open create town screen
                openCreateTownScreen();
                return true;
            }

            // Check for refresh button click
            if (selectedCategory != null &&
                (selectedCategory.getId().equals("all_towns") || selectedCategory.getId().equals("town_ranking"))) {
                int leftX = (width - panelWidth) / 2;
                int topY = (height - panelHeight) / 2;

                // Use the same calculations as in rendering for consistency
                int contentX = leftX + SPACING_MD;
                int contentY = topY + SPACING_LG + 12 + TAB_HEIGHT + SPACING_SM;
                int contentWidth = panelWidth - SPACING_MD * 2;

                // Position the refresh button on the far right with some margin
                int refreshButtonX = contentX + contentWidth - 30;
                int refreshButtonY = contentY + 8;

                // Check if mouse is hovering over the refresh button
                if (mouseX >= refreshButtonX - 2 && mouseX <= refreshButtonX + 12 &&
                    mouseY >= refreshButtonY - 2 && mouseY <= refreshButtonY + 12) {
                    // Play click sound
                    this.client.getSoundManager().play(net.minecraft.client.sound.PositionedSoundInstance.master(
                        net.minecraft.sound.SoundEvents.UI_BUTTON_CLICK, 1.0F));

                    // Refresh the category
                    refreshCategory(selectedCategory);
                    return true;
                }
            }
        }

        // Check for clicks on town entries
        if (button == 0 && selectedCategory != null) { // Left click
            int leftX = (width - panelWidth) / 2;
            int topY = (height - panelHeight) / 2;

            // Use the same calculations as in rendering for consistency
            int contentX = leftX + SPACING_MD;
            int contentY = topY + SPACING_LG + 12 + TAB_HEIGHT + SPACING_SM;
            int contentWidth = panelWidth - SPACING_MD * 2;
            int buttonWidth = contentWidth - 30;

            // Calculate visible area for entries - match rendering calculations
            int entriesAreaY = contentY + 25; // Start entries closer to header
            int contentHeight = panelHeight - (SPACING_LG + 12 + TAB_HEIGHT + SPACING_SM + SPACING_LG + 24);
            int entriesAreaHeight = contentHeight - 35; // More vertical space for entries

            // Check for clicks on scrollbar
            if (mouseX >= contentX + contentWidth - 8 && mouseX <= contentX + contentWidth - 4 &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate total height of all entries including expanded ones
                int totalHeight = 0;
                for (TownEntry entry : selectedCategory.getEntries()) {
                    int entryHeight = calculateExpandedEntryHeight(entry, buttonWidth);
                    totalHeight += entryHeight + ENTRY_SPACING;
                }

                // Calculate max scroll
                int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

                if (maxScroll > 0) {
                    // Calculate new scroll position based on click position
                    float clickPosition = (float)(mouseY - entriesAreaY) / entriesAreaHeight;
                    scrollOffset = Math.round(clickPosition * maxScroll);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                    return true;
                }
            }

            // Check if mouse is in the entries area
            if (mouseX >= contentX + 10 && mouseX <= contentX + 10 + buttonWidth &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate positions with scrolling
                List<TownEntry> entries = selectedCategory.getEntries();
                int entryY = entriesAreaY - scrollOffset;

                for (TownEntry entry : entries) {
                    // Calculate entry height based on expanded state - match rendering exactly
                    int entryHeight = calculateExpandedEntryHeight(entry, buttonWidth);

                    // Skip if entry is completely outside visible area
                    if (entryY + entryHeight < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                        entryY += entryHeight + ENTRY_SPACING;
                        continue;
                    }

                    // FIRST: Check if click is on the join button - match rendering logic exactly
                    // This must be done BEFORE any other click handling to ensure correct entryY position
                    boolean canJoin = true;
                    boolean isPlayerInTown = false;
                    if (client.player != null) {
                        Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
                        if (playerTown != null && playerTown.getId().equals(entry.getTown().getId())) {
                            canJoin = false;
                            isPlayerInTown = true;
                        }
                    }

                    // Check if town is closed - match rendering logic exactly
                    boolean isTownClosed = !entry.getTown().isOpen();

                    // Determine button width based on town status and player membership - match rendering logic exactly
                    int joinButtonWidth;
                    if (isPlayerInTown) {
                        joinButtonWidth = 20;
                    } else if (isTownClosed) {
                        joinButtonWidth = 40; // Same size as join button
                        canJoin = false; // Can't join closed towns
                    } else {
                        joinButtonWidth = 40;
                    }

                    int buttonX = contentX + buttonWidth - 45; // Match rendering: contentX + buttonWidth - 45
                    int buttonY = entryY + (ENTRY_HEIGHT - 16) / 2; // Match rendering: entryY + (ENTRY_HEIGHT - 16) / 2

                    // Debug: Ensure exact match with rendering coordinates
                    if (mouseX >= buttonX && mouseX <= buttonX + joinButtonWidth &&
                        mouseY >= buttonY && mouseY <= buttonY + 16) {

                        if (canJoin && !isTownClosed) {
                            // Play click sound
                            playClickSound();

                            // Join the town
                            joinTown(entry.getTown());
                            return true;
                        } else if (isTownClosed) {
                            // Play a different sound or show a message for closed towns
                            playClickSound();
                            setStatus("This town is closed to new members", Formatting.RED);
                            return true;
                        }
                    }

                    // SECOND: Check if click is on the town entry (but not on the join button area)
                    // Make the clickable area more precise - exclude the join button area completely
                    if (mouseX >= contentX + 10 && mouseX <= buttonX - 5 && // Stop before join button with margin
                        mouseY >= entryY && mouseY <= entryY + ENTRY_HEIGHT) {

                        // Play click sound
                        playClickSound();

                        // Close all other expanded entries first (single expand behavior)
                        for (TownEntry otherEntry : entries) {
                            if (otherEntry != entry && otherEntry.isExpanded()) {
                                otherEntry.setExpanded(false);
                            }
                        }

                        // Toggle expanded state of clicked entry
                        boolean wasExpanded = entry.isExpanded();
                        entry.toggleExpanded();

                        // Recalculate total height to update scrolling
                        int totalHeight = 0;
                        for (TownEntry e : entries) {
                            int eHeight = calculateExpandedEntryHeight(e, buttonWidth);
                            totalHeight += eHeight + ENTRY_SPACING;
                        }
                        // Add extra padding at the bottom to ensure we can scroll to see the last entry fully
                        totalHeight += 30;

                        // Adjust scroll if needed to keep the expanded entry visible
                        int visibleAreaHeight = entriesAreaHeight; // Use the already defined variable
                        int maxScroll = Math.max(0, totalHeight - visibleAreaHeight);

                        // If we're expanding and the expanded content would be off-screen, scroll to show it
                        if (!wasExpanded && entry.isExpanded()) {
                            // Check if the expanded content would be below the visible area
                            int expandedHeight = calculateExpandedEntryHeight(entry, buttonWidth);
                            int expandedBottom = entryY + expandedHeight;
                            if (expandedBottom > entriesAreaY + visibleAreaHeight) {
                                // Scroll down just enough to show the expanded content
                                scrollOffset = Math.min(maxScroll, scrollOffset + (expandedBottom - (entriesAreaY + visibleAreaHeight)));
                            }
                        }

                        return true;
                    }

                    // Move to next entry
                    entryY += entryHeight + ENTRY_SPACING;
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Handle scrolling in the entries area
        if (selectedCategory != null) {
            int leftX = (width - panelWidth) / 2;
            int topY = (height - panelHeight) / 2;

            // Use the same calculations as in rendering for consistency
            int contentX = leftX + SPACING_MD;
            int contentY = topY + SPACING_LG + 12 + TAB_HEIGHT + SPACING_SM;
            int contentWidth = panelWidth - SPACING_MD * 2;
            int buttonWidth = contentWidth - 30;

            // Calculate visible area for entries - match rendering calculations
            int entriesAreaY = contentY + 25; // Start entries closer to header
            int contentHeight = panelHeight - (SPACING_LG + 12 + TAB_HEIGHT + SPACING_SM + SPACING_LG + 24);
            int entriesAreaHeight = contentHeight - 35; // More vertical space for entries

            // Check if mouse is in the entries area
            if (mouseX >= contentX + 10 && mouseX <= contentX + 10 + buttonWidth &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate total height of all entries including expanded ones
                int totalHeight = 0;
                for (TownEntry entry : selectedCategory.getEntries()) {
                    int entryHeight = calculateExpandedEntryHeight(entry, buttonWidth);
                    totalHeight += entryHeight + ENTRY_SPACING;
                }
                // Add extra padding at the bottom to ensure we can scroll to see the last entry fully
                totalHeight += 30;

                // Calculate max scroll
                int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

                // Update scroll offset with smoother scrolling
                scrollOffset -= (int) (amount * SCROLL_AMOUNT);

                // Ensure scroll offset stays within bounds
                scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));

                return true;
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Opens the create town screen.
     */
    private void openCreateTownScreen() {
        // Play click sound
        playClickSound();

        // Open the create town screen
        client.setScreen(new CreateTownScreen(this));
    }

    /**
     * Refreshes the towns in a category.
     *
     * @param category The category to refresh
     */
    private void refreshCategory(TownCategory category) {
        if (category == null) return;

        // Clear the category entries
        category.getEntries().clear();

        // Refresh based on category type
        if (category.getId().equals("all_towns")) {
            // Refresh all towns
            // Use ClientTownManager for better client-side caching and synchronization
            List<Town> allTowns = new ArrayList<>(com.pokecobble.town.client.ClientTownManager.getInstance().getAllTowns());

            // Fallback to TownManager if ClientTownManager has no towns
            if (allTowns.isEmpty()) {
                allTowns = new ArrayList<>(TownManager.getInstance().getAllTowns());

                // If both sources are empty, request fresh data from server
                if (allTowns.isEmpty()) {
                    com.pokecobble.town.network.town.TownNetworkHandler.requestTownList();
                    Pokecobbleclaim.LOGGER.info("No towns found in cache during refresh, requesting fresh town list from server");
                }
            }
            for (Town town : allTowns) {
                TownEntry entry = new TownEntry(
                    town.getId().toString(),
                    town.getName(),
                    town.getPlayerCount() + " " + (town.getPlayerCount() == 1 ? "player" : "players"),
                    town
                );
                category.addEntry(entry);
            }

            // Show status message
            setStatus("All Towns refreshed", Formatting.GREEN);
        } else if (category.getId().equals("town_ranking")) {
            // Refresh town ranking
            // Use ClientTownManager for better client-side caching and synchronization
            List<Town> allTowns = new ArrayList<>(com.pokecobble.town.client.ClientTownManager.getInstance().getAllTowns());

            // Fallback to TownManager if ClientTownManager has no towns
            if (allTowns.isEmpty()) {
                allTowns = new ArrayList<>(TownManager.getInstance().getAllTowns());

                // If both sources are empty, request fresh data from server
                if (allTowns.isEmpty()) {
                    com.pokecobble.town.network.town.TownNetworkHandler.requestTownList();
                    Pokecobbleclaim.LOGGER.info("No towns found in cache during ranking refresh, requesting fresh town list from server");
                }
            }

            List<Town> sortedTowns = new ArrayList<>(allTowns);
            sortedTowns.sort((t1, t2) -> Integer.compare(t2.getPlayerCount(), t1.getPlayerCount()));

            for (int i = 0; i < Math.min(5, sortedTowns.size()); i++) {
                Town town = sortedTowns.get(i);
                TownEntry entry = new TownEntry(
                    town.getId().toString(),
                    town.getName(),
                    town.getPlayerCount() + " " + (town.getPlayerCount() == 1 ? "player" : "players"),
                    town
                );
                category.addEntry(entry);
            }

            // Show status message
            setStatus("Town Ranking refreshed", Formatting.GREEN);
        }

        // Reset scroll offset
        scrollOffset = 0;
    }

    /**
     * Joins a town.
     *
     * @param town The town to join
     */
    private void joinTown(Town town) {
        if (client.player == null) {
            return;
        }

        // Check if player is already in a town
        Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
        if (playerTown != null) {
            if (playerTown.getId().equals(town.getId())) {
                setStatus("You are already in this town", Formatting.YELLOW);
            } else {
                setStatus("You must leave your current town first", Formatting.RED);
            }
            return;
        }

        // Send join request to server
        com.pokecobble.town.network.town.TownNetworkHandler.requestTownJoin(town.getId());

        // Set status to indicate request was sent
        setStatus("Joining " + town.getName() + "...", Formatting.YELLOW);

        // Play request sound
        playClickSound();
    }

    /**
     * Sets the status text with modern styling and auto-hide.
     *
     * @param message The message to display
     * @param formatting The formatting to apply
     */
    private void setStatus(String message, Formatting formatting) {
        statusText = Text.literal(message).formatted(formatting);
        statusShowTime = System.currentTimeMillis();

        // Set modern color based on formatting
        if (formatting == Formatting.RED) {
            statusColor = ACCENT_DANGER; // Modern red
        } else if (formatting == Formatting.GREEN) {
            statusColor = ACCENT_SUCCESS; // Modern green
        } else if (formatting == Formatting.YELLOW) {
            statusColor = ACCENT_WARNING; // Modern orange/yellow
        } else {
            statusColor = TEXT_PRIMARY; // Modern white
        }
    }

    @Override
    public void close() {
        client.setScreen(parent);
    }

    /**
     * Calculates the dynamic height of an expanded entry based on description length.
     *
     * @param entry The town entry
     * @param buttonWidth The width available for content
     * @return The calculated height for the expanded entry
     */
    private int calculateExpandedEntryHeight(TownEntry entry, int buttonWidth) {
        if (!entry.isExpanded()) {
            return ENTRY_HEIGHT;
        }

        // Base height for the entry header
        int height = ENTRY_HEIGHT;

        // Add height for description
        String description = entry.getTown().getDescription();
        int descriptionMaxWidth = buttonWidth - 40;
        java.util.List<String> wrappedDescription = wrapTextToCharacterLimit(description, 100, descriptionMaxWidth);

        // Height for "Description:" label + wrapped lines + spacing
        height += 12 + (wrappedDescription.size() * 10) + 10;

        // Height for the expandable info section at the bottom (status, players, created)
        height += 25; // Two rows of info

        return height;
    }

    /**
     * Wraps text to fit within a character limit and pixel width, supporting multiple lines.
     *
     * @param text The text to wrap
     * @param characterLimit The maximum number of characters per line
     * @param maxPixelWidth The maximum width in pixels
     * @return A list of wrapped text lines
     */
    private java.util.List<String> wrapTextToCharacterLimit(String text, int characterLimit, int maxPixelWidth) {
        java.util.List<String> lines = new java.util.ArrayList<>();
        if (text == null || text.isEmpty()) {
            return lines;
        }

        String[] words = text.split(" ");
        StringBuilder currentLine = new StringBuilder();

        for (String word : words) {
            // Check if adding this word would exceed character limit or pixel width
            String testLine = currentLine.length() == 0 ? word : currentLine + " " + word;

            if (testLine.length() <= characterLimit && this.textRenderer.getWidth(testLine) <= maxPixelWidth) {
                // Word fits, add it to current line
                if (currentLine.length() > 0) {
                    currentLine.append(" ");
                }
                currentLine.append(word);
            } else {
                // Word doesn't fit, start new line
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder();
                }

                // Handle case where single word is too long
                if (word.length() > characterLimit || this.textRenderer.getWidth(word) > maxPixelWidth) {
                    // Split the word
                    int i = 0;
                    while (i < word.length()) {
                        StringBuilder partialWord = new StringBuilder();
                        while (i < word.length() &&
                               partialWord.length() < characterLimit &&
                               this.textRenderer.getWidth(partialWord.toString() + word.charAt(i)) <= maxPixelWidth) {
                            partialWord.append(word.charAt(i));
                            i++;
                        }
                        if (partialWord.length() > 0) {
                            lines.add(partialWord.toString());
                        }
                    }
                } else {
                    currentLine.append(word);
                }
            }
        }

        // Add the last line if not empty
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        return lines;
    }

    /**
     * Class representing a town category
     */
    public static class TownCategory {
        private final String id;
        private final String name;
        private final String description;
        private final int color;
        private final List<TownEntry> entries = new ArrayList<>();
        private int x;
        private int y;
        private int width = TAB_MIN_WIDTH; // Default width

        public TownCategory(String id, String name, String description, int color) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.color = color;
        }

        public void addEntry(TownEntry entry) {
            entries.add(entry);
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public String getDescription() {
            return description;
        }

        public int getColor() {
            return color;
        }

        public List<TownEntry> getEntries() {
            return entries;
        }

        public void setPosition(int x, int y) {
            this.x = x;
            this.y = y;
        }

        public int getX() {
            return x;
        }

        public int getY() {
            return y;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getWidth() {
            return width;
        }
    }

    /**
     * Class representing a town entry
     */
    public static class TownEntry {
        private final String id;
        private final String name;
        private final String description;
        private final Town town;
        private boolean expanded = false;

        public TownEntry(String id, String name, String description, Town town) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.town = town;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public String getDescription() {
            return description;
        }

        public Town getTown() {
            return town;
        }

        public boolean isExpanded() {
            return expanded;
        }

        public void setExpanded(boolean expanded) {
            this.expanded = expanded;
        }

        public void toggleExpanded() {
            this.expanded = !this.expanded;
        }
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Called when the town list is updated.
     * Implements the TownListUpdateCallback interface.
     */
    @Override
    public void onTownListUpdate() {
        // Refresh the categories to update the town list
        setupCategories();
    }

    /**
     * Called when the screen is removed.
     * Unregisters from town list updates.
     */
    @Override
    public void removed() {
        super.removed();

        // Unregister from town list updates when screen is closed
        TownDataSynchronizer.unregisterTownListUpdateCallback(this);
    }

    /**
     * Implements RefreshableComponent interface.
     */
    @Override
    public void refresh() {
        setupCategories();

        // Request fresh data from server
        com.pokecobble.town.network.town.TownNetworkHandler.requestTownData();
    }

    /**
     * Gets the data types this component depends on.
     */
    @Override
    public java.util.Set<String> getDependentDataTypes() {
        return java.util.Set.of("town_data");
    }
}
