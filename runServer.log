
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJ<PERSON> UP-TO-DATE
> Task :processResources UP-TO-DATE
> Task :classes UP-TO-DATE
> Task :jar UP-TO-DATE
> Task :compileTestJava NO-SOURCE
> Task :processIncludeJars UP-TO-DATE
> Task :remapJar UP-TO-DATE
> Task :sourcesJar UP-TO-DATE
> Task :remapSourcesJar UP-TO-DATE
> Task :assemble UP-TO-DATE
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test NO-SOURCE
> Task :validateAccessWidener NO-SOURCE
> Task :check UP-TO-DATE
> Task :build UP-TO-DATE
> Task :cleanRun UP-TO-DATE
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE

> Task :runServer
[34m[17:45:35][m [32m[main/INFO][m [36m(FabricLoader/GameProvider)[m [0mLoading Minecraft 1.20.1 with Fabric Loader 0.16.13
[m[34m[17:45:35][m [32m[main/INFO][m [36m(FabricLoader)[m [0mLoading 46 mods:
	- fabric-api 0.92.5*****.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 21
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[m[34m[17:45:35][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mSpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=SERVER
[m[34m[17:45:35][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mLoaded Fabric development mappings for mixin remapper!
[m[34m[17:45:35][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mCompatibility level set to JAVA_17
[m[34m[17:45:36][m [32m[main/INFO][m [36m(FabricLoader/MixinExtras|Service)[m [0mInitializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim mod
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side network handlers
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mReset all town and player data versions
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mFound 2 individual town files, loading...
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaa' with 2 players from d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaaaaa' with 6 players from 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRestored 8 player-town relationships
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded 2 towns from individual files
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mTown data loading completed. Final town count in TownManager: 2
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim mod initialized successfully
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim server components
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded backup configuration
[m[34m[17:45:42][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim server components initialized successfully
[m[34m[17:45:42][m [32m[main/INFO][m [36m(Minecraft)[m [0mEnvironment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[m[34m[17:45:43][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side town commands
[m[34m[17:45:43][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistering town admin commands
[m[34m[17:45:43][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mTown admin commands registered successfully
[m[34m[17:45:43][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistered backup command
[m[34m[17:45:43][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mServer-side town commands registered successfully
[m[34m[17:45:43][m [32m[main/INFO][m [36m(Minecraft)[m [0mLoaded 7 recipes
[m[34m[17:45:43][m [32m[main/INFO][m [36m(Minecraft)[m [0mLoaded 1271 advancements
[m[34m[17:45:44][m [32m[main/INFO][m [36m(BiomeModificationImpl)[m [0mApplied 0 biome modifications to 0 of 64 new biomes in 1.808 ms
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSetting server instance in TownManager and PlayerDataManager
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mServer instance set in TownManager
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPlayer data storage system initialized
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPlayerDataManager initialized
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0m[System] Error logging system initialized
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0m[Server] Server started
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mReal-time status manager initialized
[m[34m[17:45:44][m [31m[pool-3-thread-1/ERROR][m [36m(pokecobbleclaim)[m [31mError updating fast status: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[17:45:44][m [31m[pool-3-thread-2/ERROR][m [36m(pokecobbleclaim)[m [31mError updating normal status: Cannot invoke "net.minecraft.server.PlayerManager.getPlayer(java.util.UUID)" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading town data during server startup
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mFound 2 individual town files, loading...
[m[34m[17:45:44][m [31m[pool-3-thread-1/ERROR][m [36m(pokecobbleclaim)[m [31mError updating slow status: Cannot invoke "net.minecraft.server.PlayerManager.getPlayer(java.util.UUID)" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaa' with 2 players from d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaaaaa' with 6 players from 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored 8 player-town relationships
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded 2 towns from individual files
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mTown data loading completed. Final town count in TownManager: 2
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPreparing town data synchronization for 2 loaded towns
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mReset all town and player data versions
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mStarting minecraft server version 1.20.1
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mLoading properties
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mDefault game type: SURVIVAL
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mGenerating keypair
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mStarting Minecraft server on *:25565
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mUsing epoll channel type
[m[34m[17:45:44][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0m**** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[m[34m[17:45:44][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0mThe server will make no attempt to authenticate usernames. Beware.
[m[34m[17:45:44][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0mWhile this makes the game possible to play without internet access, it also opens up the ability for hackers to connect with any username they choose.
[m[34m[17:45:44][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0mTo change this, set "online-mode" to "true" in the server.properties file.
[m[34m[17:45:44][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPreparing level "world"
[m[34m[17:45:45][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPreparing start region for dimension minecraft:overworld
[m[34m[17:45:47][m [32m[Worker-Main-5/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[17:45:47][m [32m[Worker-Main-14/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[17:45:47][m [32m[Worker-Main-12/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[17:45:47][m [32m[Worker-Main-5/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[17:45:48][m [32m[Worker-Main-12/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[17:45:48][m [32m[Worker-Main-13/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[17:45:48][m [32m[Worker-Main-10/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 24%
[m[34m[17:45:48][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mTime elapsed: 3206 ms
[m[34m[17:45:48][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mDone (4.139s)! For help, type "help"
[m[34m[17:45:52][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPlayer194[/127.0.0.1:38556] logged in with entity id 161 at (3.5, 84.0, 10.5)
[m[34m[17:45:52][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mCreated new player data for Player194
[m[34m[17:45:52][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:52][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:52][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:52][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:52][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:52][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:52][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSent complete town data to new player Player194 (2 towns)
[m[34m[17:45:52][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPlayer Player194 is not in any town
[m[34m[17:45:52][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSynchronized 2 towns to player Player194
[m[34m[17:45:52][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPlayer194 joined the game
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mProcessing town join request from player Player194 for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mFound town: aaaaaaaaaaaaa (players: 6/20, type: OPEN)
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mAttempting to add player Player194 to town aaaaaaaaaaaaa
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mCreating TownPlayer: Player194 (1de0626e-6c80-37c2-856b-2a8735302b74) with rank MEMBER for town aaaaaaaaaaaaa
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[17:45:56][m [33m[Netty Epoll Server IO #2/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mBroadcasted town list update to 1 players (2 towns)
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mHandled player town membership change: joined town aaaaaaaaaaaaa for player 1de0626e-6c80-37c2-856b-2a8735302b74
[m[34m[17:45:56][m [33m[Netty Epoll Server IO #2/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mBroadcasted player list update for town aaaaaaaaaaaaa to 1 players
[m[34m[17:45:56][m [33m[Netty Epoll Server IO #2/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mBroadcasted town list update to 1 players (2 towns)
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mPlayer 1de0626e-6c80-37c2-856b-2a8735302b74 joined town aaaaaaaaaaaaa (now 7 players)
[m[34m[17:45:56][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mSUCCESS: Player Player194 joined town: aaaaaaaaaaaaa
[m[34m[17:45:56][m [33m[Netty Epoll Server IO #2/WARN][m [36m(pokecobbleclaim)[m [0mRate limited packet to player: Player194
[m[34m[17:45:59][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mProcessing town join request from player Player194 for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[17:45:59][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mPlayer Player194 is already in town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[17:46:01][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mProcessing town join request from player Player194 for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[17:46:01][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mPlayer Player194 is already in town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[17:46:01][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mProcessing town join request from player Player194 for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[17:46:01][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mPlayer Player194 is already in town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m